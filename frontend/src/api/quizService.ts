import api from './apiClient';
import type { QuizQuestion, QuizQuestionsResponse } from '../types/quiz.types';

// 知识问答API
export const getQuizQuestions = (limit?: number) =>
  api.get<QuizQuestionsResponse>('/quiz/questions', { params: { limit } });

export const submitQuizAnswer = (questionId: string, selectedOption: string) =>
  api.post('/quiz/submit', { questionId, selectedOption });

export const getQuizLeaderboard = () =>
  api.get('/quiz/leaderboard');
