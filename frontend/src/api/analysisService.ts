import apiClient from './apiClient';
import { AxiosError, AxiosResponse } from 'axios';

export interface AnalysisRequestItem {
  buoy_id: string;
  sensor_type: string;
  data: {
    timestamp: string;
    value: number;
    unit: string;
  };
}

export interface AnalysisRequestBody {
  items: AnalysisRequestItem[];
}

export interface AnalysisResponse {
  analysis_text: string;
}

export async function postAnalysisReport(body: AnalysisRequestBody): Promise<AnalysisResponse> {
  try {
    console.log('发送分析请求数据:', JSON.stringify(body, null, 2));
    
    const response = await apiClient.post<AnalysisResponse>('/analysis/report', body);
    return response.data;
  } catch (error: unknown) {
    if (error instanceof AxiosError) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('分析请求超时，请稍后重试');
      }
      if (error.code === 'ERR_NETWORK') {
        throw new Error('网络连接失败，请检查网络设置');
      }
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;
        
        // 处理特定的错误状态码
        if (status === 429) {
          throw new Error('请求过于频繁，请稍后再试');
        }
        if (status >= 500) {
          throw new Error('服务器暂时不可用，请稍后重试');
        }
        if (status === 422) {
          throw new Error('请求数据格式不正确，请检查输入');
        }
        
        // 尝试从响应中获取错误信息
        const errorMessage = data?.detail || data?.message || '分析请求失败';
        throw new Error(errorMessage);
      }
    }
    // 其他未知错误
    throw new Error('分析服务发生未知错误，请稍后重试');
  }
}