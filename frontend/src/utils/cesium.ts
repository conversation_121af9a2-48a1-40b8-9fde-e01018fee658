import * as Cesium from 'cesium';
import { Buoy } from '../types';

// 初始化Cesium
export const initCesium = (): void => {
  // 使用vite-plugin-cesium插件处理Cesium资源，无需显式设置Token
  // 如果需要自定义令牌，可以取消下面一行的注释并设置你的令牌
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmNTg1NzNhOC00YjkzLTRhYjAtOTg4OC1hNDhiOWFlZmRkNjUiLCJpZCI6Mjk5Nzk1LCJpYXQiOjE3NDY1MDIxODJ9.zXa3Slolwuvru03vAO59Fgfoj7i2Zgbnqbi4roMZUg8';
};

// 根据状态获取浮标颜色
export const getBuoyColor = (status: string): Cesium.Color => {
  switch (status) {
    case 'active':
      return Cesium.Color.GREENYELLOW;
    case 'inactive':
      return Cesium.Color.GRAY;
    case 'error':
      return Cesium.Color.RED;
    default:
      return Cesium.Color.BLUE;
  }
};

// 创建浮标实体
export const createBuoyEntity = (buoy: Buoy, selectedBuoyId: string | null = null): Cesium.Entity => {
  const position = Cesium.Cartesian3.fromDegrees(
    buoy.location.longitude,
    buoy.location.latitude,
    0  // 设置高度为0，让点贴地
  );
  
  let color = getBuoyColor(buoy.status);
  if (selectedBuoyId && buoy.id === selectedBuoyId) {
    color = Cesium.Color.YELLOW; // 为选中浮标设置绿色
  }
  
  return new Cesium.Entity({
    id: buoy.id,
    name: buoy.name,
    description: buoy.description,
    position,
    point: {
      pixelSize: 7,
      color,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 1,
      heightReference: Cesium.HeightReference.NONE  // 设置为贴地模式
    }
  });
};

// 获取浮标状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'active':
      return '<span style="color: green;">在线</span>';
    case 'inactive':
      return '<span style="color: gray;">离线</span>';
    case 'error':
      return '<span style="color: red;">故障</span>';
    default:
      return '<span style="color: blue;">未知</span>';
  }
};

// 飞行到指定浮标位置
export const flyToBuoy = (viewer: Cesium.Viewer, buoy: Buoy): void => {
  try {
    // 确保浮标的位置有效
    if (!buoy.location || typeof buoy.location.longitude !== 'number' || typeof buoy.location.latitude !== 'number') {
      console.error('浮标位置数据无效:', buoy);
      return;
    }
    
    // 检查当前视图模式
    const is2D = viewer.scene.mode === Cesium.SceneMode.SCENE2D;
    
    if (is2D) {
      // 在2D模式下使用更安全的视图设置方法
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(
          buoy.location.longitude,
          buoy.location.latitude,
          800000 // 高度（米）
        ),
      });
    } else {
      // 3D和哥伦布模式下使用飞行动画
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          buoy.location.longitude,
          buoy.location.latitude,
          800000 // 高度（米）
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0), // 朝向正北
          pitch: Cesium.Math.toRadians(-90), // 垂直向下俯视
          roll: 0.0,
        },
        duration: 1.5,
      });
    }
  } catch (e) {
    console.error('飞行到浮标位置时出错:', e);
    
    // 尝试重置为安全视图
    try {
      viewer.camera.setView({
        destination: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
      });
    } catch (resetError) {
      console.error('无法重置视图:', resetError);
    }
  }
};

// 创建海洋图层（如果需要）
export const addOceanLayer = (viewer: Cesium.Viewer): void => {
  viewer.scene.globe.enableLighting = true;
  viewer.scene.globe.baseColor = Cesium.Color.BLUE.brighten(0.6, new Cesium.Color());
  
  // 可以添加更复杂的海洋层设置，如波浪效果等
};

// 清除所有浮标实体
export const clearBuoys = (viewer: Cesium.Viewer): void => {
  viewer.entities.removeAll();
};

// 加载浮标数据并显示在地图上
export const loadBuoysToMap = (viewer: Cesium.Viewer, buoys: Buoy[], selectedBuoyId: string | null = null): void => {
  // console.log('buoys', buoys);
  // 先清除所有现有实体，避免ID冲突
  viewer.entities.removeAll();
  buoys.forEach(buoy => {
    viewer.entities.add(createBuoyEntity(buoy, selectedBuoyId));
  });
};