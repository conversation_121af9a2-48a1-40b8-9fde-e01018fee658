import React, { useState, useEffect } from 'react';
import { FiDatabase, FiTrash2, FiBarChart2, FiAlertCircle } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import ConfirmDialog from './ConfirmDialog';
import StatsCard from './StatsCard';
import { getDatabaseStats, cleanupSensorData, cleanupNotifications, cleanupControlLogs } from '../../api/adminService';

const DataManagement: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cleanupType, setCleanupType] = useState<string | null>(null);
  const [cleanupDays, setCleanupDays] = useState(30);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  
  const { showToast } = useToast();

  // 加载数据库统计信息
  const loadStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getDatabaseStats();
      setStats(response.data);
    } catch (err) {
      console.error('加载数据库统计信息失败:', err);
      setError('加载数据库统计信息失败，请稍后重试');
      showToast('加载数据库统计信息失败，请稍后重试', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadStats();
  }, []);

  // 处理清理数据
  const handleCleanupClick = (type: string) => {
    setCleanupType(type);
    setShowConfirmDialog(true);
  };

  // 确认清理数据
  const confirmCleanup = async () => {
    if (!cleanupType) return;
    
    setCleanupLoading(true);
    try {
      let response;
      
      switch (cleanupType) {
        case 'sensor-data':
          response = await cleanupSensorData(cleanupDays);
          break;
        case 'notifications':
          response = await cleanupNotifications(cleanupDays);
          break;
        case 'control-logs':
          response = await cleanupControlLogs(cleanupDays);
          break;
        default:
          throw new Error('未知的清理类型');
      }
      
      showToast(response.data.message, 'success');
      
      // 重新加载统计信息
      await loadStats();
    } catch (err) {
      console.error('清理数据失败:', err);
      showToast('清理数据失败，请稍后重试', 'error');
    } finally {
      setCleanupLoading(false);
      setShowConfirmDialog(false);
      setCleanupType(null);
    }
  };

  // 获取清理类型的显示名称
  const getCleanupTypeName = (type: string) => {
    switch (type) {
      case 'sensor-data':
        return '传感器数据';
      case 'notifications':
        return '通知';
      case 'control-logs':
        return '控制日志';
      default:
        return '数据';
    }
  };

  const { colors } = useTheme();
  return (
    <div style={{ backgroundColor: colors.background.main }}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4" style={{ color: colors.text.primary }}>数据管理</h2>
        <p style={{ color: colors.text.secondary }}>
          在这里您可以查看数据库统计信息，并清理历史数据以优化系统性能。
        </p>
      </div>

      {/* 数据库统计信息 */}
      {loading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      ) : error ? (
        <div className="text-center py-10" style={{ color: colors.buoy.error }}>{error}</div>
      ) : stats ? (
        <div>
          <h3 className="text-lg font-medium mb-4" style={{ color: colors.text.primary }}>数据库统计信息</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <StatsCard
              title="用户数量"
              value={stats.users || 0}
              icon={<FiDatabase />}
            />
            <StatsCard
              title="浮标数量"
              value={stats.buoys || 0}
              icon={<FiDatabase />}
            />
            <StatsCard
              title="传感器数据"
              value={stats.sensor_data || 0}
              icon={<FiBarChart2 />}
              description={`最近7天: ${stats.sensor_data_last_7_days || 0}`}
            />
            <StatsCard
              title="图像数据"
              value={stats.images || 0}
              icon={<FiDatabase />}
            />
            <StatsCard
              title="控制日志"
              value={stats.control_logs || 0}
              icon={<FiDatabase />}
            />
            <StatsCard
              title="通知"
              value={stats.notifications || 0}
              icon={<FiAlertCircle />}
            />
          </div>

          {stats.database_size && (
            <div className="overflow-hidden shadow rounded-lg mb-8" style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}>
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium mb-4" style={{ color: colors.text.primary }}>数据库大小信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 rounded-md" style={{ backgroundColor: colors.background.secondary }}>
                    <div className="text-sm font-medium" style={{ color: colors.text.secondary }}>数据库总大小</div>
                    <div className="mt-1 text-2xl font-semibold" style={{ color: colors.text.primary }}>{stats.database_size}</div>
                  </div>
                  <div className="p-4 rounded-md" style={{ backgroundColor: colors.background.secondary }}>
                    <div className="text-sm font-medium" style={{ color: colors.text.secondary }}>传感器数据表大小</div>
                    <div className="mt-1 text-2xl font-semibold" style={{ color: colors.text.primary }}>{stats.sensor_data_table_size}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <h3 className="text-lg font-medium mb-4" style={{ color: colors.text.primary }}>数据清理</h3>
          
          <div className="overflow-hidden shadow rounded-lg mb-8" style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}>
            <div className="px-4 py-5 sm:p-6">
              <h4 className="text-base font-medium mb-2" style={{ color: colors.text.primary }}>清理历史数据</h4>
              <p className="text-sm mb-4" style={{ color: colors.text.secondary }}>
                您可以清理指定天数前的历史数据，以优化数据库性能。清理操作不可撤销，请谨慎操作。
              </p>
              
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md" style={{ backgroundColor: colors.background.secondary }}>
                  <div>
                    <h5 className="font-medium" style={{ color: colors.text.primary }}>传感器数据</h5>
                    <p className="text-sm" style={{ color: colors.text.secondary }}>清理指定天数前的传感器数据记录</p>
                  </div>
                  <button
                    onClick={() => handleCleanupClick('sensor-data')}
                    className="mt-2 sm:mt-0 px-4 py-2 rounded-md flex items-center text-white"
                    style={{ backgroundColor: colors.buoy.error }}
                  >
                    <FiTrash2 className="mr-2" />
                    清理数据
                  </button>
                </div>
                
                <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md" style={{ backgroundColor: colors.background.secondary }}>
                  <div>
                    <h5 className="font-medium" style={{ color: colors.text.primary }}>通知</h5>
                    <p className="text-sm" style={{ color: colors.text.secondary }}>清理指定天数前的通知记录</p>
                  </div>
                  <button
                    onClick={() => handleCleanupClick('notifications')}
                    className="mt-2 sm:mt-0 px-4 py-2 rounded-md flex items-center text-white"
                    style={{ backgroundColor: colors.buoy.error }}
                  >
                    <FiTrash2 className="mr-2" />
                    清理数据
                  </button>
                </div>
                
                <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md" style={{ backgroundColor: colors.background.secondary }}>
                  <div>
                    <h5 className="font-medium" style={{ color: colors.text.primary }}>控制日志</h5>
                    <p className="text-sm" style={{ color: colors.text.secondary }}>清理指定天数前的控制操作日志</p>
                  </div>
                  <button
                    onClick={() => handleCleanupClick('control-logs')}
                    className="mt-2 sm:mt-0 px-4 py-2 rounded-md flex items-center text-white"
                    style={{ backgroundColor: colors.buoy.error }}
                  >
                    <FiTrash2 className="mr-2" />
                    清理数据
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-10" style={{ color: colors.text.secondary }}>暂无数据</div>
      )}

      {/* 清理确认对话框 */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        title={`确认清理${getCleanupTypeName(cleanupType || '')}`}
        message={`您确定要清理 ${cleanupDays} 天前的${getCleanupTypeName(cleanupType || '')}吗？此操作不可撤销。`}
        confirmText="确认清理"
        cancelText="取消"
        onConfirm={confirmCleanup}
        onCancel={() => setShowConfirmDialog(false)}
        isLoading={cleanupLoading}
      />
    </div>
  );
};

export default DataManagement;
