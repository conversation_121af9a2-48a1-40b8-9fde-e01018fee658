import React, { useState, useEffect } from 'react';
import { FiEdit, FiTrash2, FiPlus, FiSearch } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import ConfirmDialog from './ConfirmDialog';
import BuoyForm from './BuoyForm';
import { getBuoys } from '../../api/buoyService';
import type { Buoy } from '../../types';

const BuoyManagement: React.FC = () => {
  const [buoys, setBuoys] = useState<Buoy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBuoyForm, setShowBuoyForm] = useState(false);
  const [editingBuoy, setEditingBuoy] = useState<Buoy | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [buoyToDelete, setBuoyToDelete] = useState<Buoy | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  const { showToast } = useToast();

  // 加载浮标数据
  const loadBuoys = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getBuoys();
      setBuoys(response.data);
    } catch (err) {
      console.error('加载浮标列表失败:', err);
      setError('加载浮标列表失败，请稍后重试');
      showToast('加载浮标列表失败，请稍后重试', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadBuoys();
  }, []);

  // 处理搜索
  const filteredBuoys = buoys.filter(buoy => 
    buoy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    buoy.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 处理编辑浮标
  const handleEditBuoy = (buoy: Buoy) => {
    setEditingBuoy(buoy);
    setShowBuoyForm(true);
  };

  // 处理删除浮标
  const handleDeleteClick = (buoy: Buoy) => {
    setBuoyToDelete(buoy);
    setDeleteConfirmOpen(true);
  };

  // 确认删除浮标
  const confirmDelete = async () => {
    if (!buoyToDelete) return;
    
    setDeleteLoading(true);
    try {
      // 这里需要实现删除浮标的API调用
      // await deleteBuoy(buoyToDelete.id);
      setBuoys(buoys.filter(b => b.id !== buoyToDelete.id));
      showToast(`浮标 ${buoyToDelete.name} 已成功删除`, 'success');
    } catch (err) {
      console.error('删除浮标失败:', err);
      showToast('删除浮标失败，请稍后重试', 'error');
    } finally {
      setDeleteLoading(false);
      setDeleteConfirmOpen(false);
      setBuoyToDelete(null);
    }
  };

  // 处理浮标表单提交（创建/编辑）
  const handleBuoyFormSubmit = (updatedBuoy: Buoy) => {
    if (editingBuoy) {
      // 更新现有浮标
      setBuoys(buoys.map(b => b.id === updatedBuoy.id ? updatedBuoy : b));
    } else {
      // 添加新浮标
      setBuoys([...buoys, updatedBuoy]);
    }
    setShowBuoyForm(false);
    setEditingBuoy(null);
  };

  const { colors } = useTheme();
  return (
    <div style={{ backgroundColor: colors.background.main }}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold" style={{ color: colors.text.primary }}>浮标管理</h2>
        <button
          onClick={() => {
            setEditingBuoy(null);
            setShowBuoyForm(true);
          }}
          className="px-4 py-2 rounded-md flex items-center text-white"
          style={{ backgroundColor: colors.primary }}
        >
          <FiPlus className="mr-2" />
          添加浮标
        </button>
      </div>

      {/* 搜索框 */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch style={{ color: colors.text.secondary }} />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border rounded-md leading-5 focus:outline-none sm:text-sm"
            placeholder="搜索浮标名称或描述..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ backgroundColor: colors.background.main, color: colors.text.primary, borderColor: colors.border, borderWidth: '1px' }}
          />
        </div>
      </div>

      {/* 浮标列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      ) : error ? (
        <div className="text-center py-10" style={{ color: colors.buoy.error }}>{error}</div>
      ) : filteredBuoys.length === 0 ? (
        <div className="text-center py-10" style={{ color: colors.text.secondary }}>
          {searchTerm ? '没有找到匹配的浮标' : '暂无浮标数据'}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full" style={{ borderColor: colors.border }}>
            <thead style={{ backgroundColor: colors.background.secondary }}>
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  浮标名称
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  描述
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  位置
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  状态
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  最后心跳
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
              {filteredBuoys.map((buoy) => (
                <tr key={buoy.id} style={{ borderColor: colors.border }}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium" style={{ color: colors.text.primary }}>{buoy.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm truncate max-w-xs" style={{ color: colors.text.secondary }}>{buoy.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm" style={{ color: colors.text.secondary }}>
                      {buoy.location.latitude.toFixed(6)}, {buoy.location.longitude.toFixed(6)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      style={{
                        backgroundColor: buoy.status === 'active' ? colors.buoy.active + '33' :
                                      buoy.status === 'inactive' ? colors.buoy.inactive + '33' :
                                      colors.buoy.error + '33',
                        color: buoy.status === 'active' ? colors.buoy.active :
                               buoy.status === 'inactive' ? colors.buoy.inactive :
                               colors.buoy.error
                      }}>
                      {buoy.status === 'active' ? '活跃' :
                       buoy.status === 'inactive' ? '不活跃' : '错误'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm" style={{ color: colors.text.secondary }}>
                      {new Date(buoy.last_heartbeat).toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEditBuoy(buoy)}
                      className="mr-4"
                      style={{ color: colors.primary }}
                    >
                      <FiEdit className="inline" /> 编辑
                    </button>
                    <button
                      onClick={() => handleDeleteClick(buoy)}
                      className=""
                      style={{ color: colors.buoy.error }}
                    >
                      <FiTrash2 className="inline" /> 删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 浮标表单（创建/编辑） - 这里需要实现BuoyForm组件 */}
      {showBuoyForm && (
        <div className="text-center py-10" style={{ backgroundColor: colors.background.main, color: colors.text.primary }}>
          <p>浮标表单组件正在开发中...</p>
          <button
            onClick={() => {
              setShowBuoyForm(false);
              setEditingBuoy(null);
            }}
            className="mt-4 px-4 py-2 rounded-md text-white"
            style={{ backgroundColor: colors.primary }}
          >
            关闭
          </button>
        </div>
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={deleteConfirmOpen}
        title="确认删除浮标"
        message={`您确定要删除浮标 "${buoyToDelete?.name}" 吗？此操作将同时删除与该浮标关联的所有数据，且无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        onConfirm={confirmDelete}
        onCancel={() => setDeleteConfirmOpen(false)}
        isLoading={deleteLoading}
      />
    </div>
  );
};

export default BuoyManagement;
