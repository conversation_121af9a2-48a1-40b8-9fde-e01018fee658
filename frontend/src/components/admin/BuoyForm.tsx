import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import type { Buoy } from '../../types';

interface BuoyFormProps {
  buoy: Buoy | null; // 如果为null，则是创建新浮标
  onSubmit: (buoy: Buoy) => void;
  onCancel: () => void;
}

const BuoyForm: React.FC<BuoyFormProps> = ({ buoy, onSubmit, onCancel }) => {
  const { colors } = useTheme();
  // 浮标表单将在后续实现
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" style={{ backgroundColor: colors.background.main }}>
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* 背景遮罩 */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 opacity-75" style={{ backgroundColor: colors.background.secondary }}></div>
        </div>

        {/* 对话框居中技巧 */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        {/* 表单内容 */}
        <div
          className="inline-block align-bottom rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
          style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}
        >
          <div className="px-4 pt-5 pb-4 sm:p-6 sm:pb-4" style={{ backgroundColor: colors.background.main }}>
            <div className="mb-4">
              <h3 className="text-lg leading-6 font-medium" id="modal-headline" style={{ color: colors.text.primary }}>
                {buoy ? '编辑浮标' : '创建新浮标'}
              </h3>
            </div>
            
            <div className="py-10 text-center">
              <p style={{ color: colors.text.secondary }}>浮标表单组件正在开发中...</p>
            </div>
          </div>
          
          <div className="px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse" style={{ backgroundColor: colors.background.secondary }}>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md shadow-sm px-4 py-2 text-base font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              style={{ backgroundColor: colors.background.main, color: colors.text.primary, border: `1px solid ${colors.border}` }}
              onClick={onCancel}
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuoyForm;
