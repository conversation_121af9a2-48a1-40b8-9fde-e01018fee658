import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { 
  FiHome, FiSettings,
  FiLogOut, FiChevronRight, FiChevronDown, FiMenu,
  FiUser,
  FiSidebar,
  FiUsers,
  FiDatabase,
  FiTrash2,
  FiActivity
} from 'react-icons/fi';

// 导航项类型定义
interface NavItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  path?: string;
  children?: NavItem[];
  divider?: boolean;
}

interface SidebarNavProps {
  collapsed: boolean;
  toggleCollapsed: () => void;
  className?: string;
}

interface NavItemLinkProps {
  item: NavItem;
  collapsed: boolean;
  isActive: boolean;
  theme: string;
}

const NavItemLink: React.FC<NavItemLinkProps> = ({
  item,
  collapsed,
  isActive,
  theme
}) => {
  const activeClass = theme === 'dark'
    ? 'bg-gray-700 text-blue-400'
    : 'bg-blue-100 text-blue-700';
  const hoverClass = theme === 'dark'
    ? 'hover:bg-gray-700 text-blue-400'
    : 'hover:bg-gray-100 hover:text-blue-700';
  return (
    <Link
      to={item.path || '#'}
      className={`
        flex items-center p-2 rounded-md transition-colors
        ${isActive ? activeClass : `${hoverClass} sidebar-text`}
        ${collapsed ? 'justify-center' : 'justify-between'}
      `}
      target={item.id === 'quiz' ? '_blank' : undefined}
    >
      <div className="flex items-center">
        <span className={`${collapsed ? 'text-lg' : 'text-base mr-3'}`}>
          {item.icon}
        </span>
        {!collapsed && <span>{item.title}</span>}
      </div>
      {!collapsed && item.children && (
        <FiChevronRight className="text-gray-400" />
      )}
    </Link>
  );
};

interface SubMenuProps {
  item: NavItem;
  collapsed: boolean;
  activeItemId: string;
  theme: string;
}
const SubMenu: React.FC<SubMenuProps> = ({
  item,
  collapsed,
  activeItemId,
  theme
}) => {
  // 子菜单默认展开
  const [open, setOpen] = useState(true);

  // 亮暗主题样式
  const activeClass = theme === 'dark'
    ? 'bg-gray-700 text-blue-400'
    : 'bg-blue-100 text-blue-700';
  const hoverClass = theme === 'dark'
    ? 'hover:bg-gray-700 text-blue-400'
    : 'hover:bg-gray-100 hover:text-blue-700';

  // 折叠状态下也显示所有子项（仅图标+tooltip）
  if (collapsed) {
    return (
      <div className="flex flex-col items-center mb-1">
        {item.children && item.children.map((child) => (
          <Link
            key={child.id}
            to={child.path || '#'}
            className={`
              flex flex-col items-center justify-center p-2 rounded-md transition-colors
              ${activeItemId === child.id
                ? activeClass
                : `${hoverClass} sidebar-text`}
            `}
            title={child.title}
            style={{ width: '100%' }}
          >
            <span className="text-lg">{child.icon}</span>
          </Link>
        ))}
      </div>
    );
  }

  // 非折叠时，保持原有展开/收起逻辑，且默认展开
  return (
    <div className="mb-1">
      <div
        onClick={() => setOpen(!open)}
        className={`
          flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors
          ${open
            ? (theme === 'dark' ? 'bg-gray-700 text-blue-400' : 'bg-blue-50 text-blue-700')
            : `${hoverClass} sidebar-text`}
        `}
      >
        <div className="flex items-center">
          <span className="text-base mr-3">{item.icon}</span>
          <span>{item.title}</span>
        </div>
        {open ? <FiChevronDown className="text-gray-400" /> : <FiChevronRight className="text-gray-400" />}
      </div>
      
      {open && item.children && (
        <div className="pl-4 mt-1 space-y-1">
          {item.children.map((child) => (
            <Link
              key={child.id}
              to={child.path || '#'}
              className={`
                flex items-center p-2 rounded-md transition-colors text-sm
                ${activeItemId === child.id
                  ? activeClass
                  : `${hoverClass} sidebar-text`}
              `}
            >
              <span className="text-base mr-3">{child.icon}</span>
              <span>{child.title}</span>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

// 侧边栏导航组件
const SidebarNav: React.FC<SidebarNavProps> = ({ collapsed, toggleCollapsed, className = '' }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const { user, logout, isAuthenticated } = useAuth();
  
  // 处理登出
  const handleLogout = async () => {
    try {
      logout();
      navigate('/auth');
    } catch (error) {
      console.error('登出失败', error);
    }
  };
  
  // 定义导航项
  const navItems: NavItem[] = [
    {
      id: 'home',
      title: '首页',
      icon: <FiHome />,
      path: '/home',
    },
    {
      id: 'account',
      title: '我的账户',
      icon: <FiUser />,
      path: '/account',
    },
    {
      id: 'quiz',
      title: '知识问答',
      icon: <FiHome />, // 暂时使用 FiHome 图标，可以根据需要更换
      path: '/quiz',
    },
    ...(user?.role === 'admin' ? [
      {
        id: 'admin',
        title: '管理系统',
        icon: <FiSettings />,
        children: [
          {
            id: 'users',
            title: '用户管理',
            icon: <FiUsers />,
            path: '/admin/users',
          },
          {
            id: 'buoys',
            title: '浮标管理',
            icon: <FiDatabase />,
            path: '/admin/buoys',
          },
          {
            id: 'data',
            title: '数据管理',
            icon: <FiTrash2 />,
            path: '/admin/data',
          },
          {
            id: 'monitor',
            title: '系统监控',
            icon: <FiActivity />,
            path: '/admin/monitor',
          },
        ],
      },
    ] : []),
  ];

  // 获取当前活动项ID
  const getActiveItemId = (): string => {
    let activeId = '';
    const checkActiveItem = (items: NavItem[]) => {
      for (const item of items) {
        if (item.path && location.pathname === item.path) {
          activeId = item.id;
          return true;
        }
        if (item.children) {
          const found = checkActiveItem(item.children);
          if (found) return true;
        }
      }
      return false;
    };
    checkActiveItem(navItems);
    return activeId;
  };
  
  const activeItemId = getActiveItemId();

  // 退出登录按钮样式
  const logoutBtnClass = `
    w-full flex items-center p-2 rounded-md transition-colors
    hover:bg-red-50 ${theme === 'dark' ? 'text-red-400 hover:bg-gray-700' : 'text-red-600'}
    ${collapsed ? 'justify-center' : 'justify-between'}
  `;

  return (
    <div className={`h-full flex flex-col ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-800'} ${className}`}>
      {/* 头部 - 品牌和折叠按钮 */}
      <div className={`
        flex items-center ${collapsed ? 'justify-center' : 'justify-between'}
        p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
      `}>
        {!collapsed && (
          <div className="flex items-center">
            <span className="font-bold text-lg">浮标系统</span>
          </div>
        )}
        <button
          onClick={toggleCollapsed}
          className={`
            w-8 h-8 flex items-center justify-center rounded-full
            ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}
            ${collapsed ? 'mx-auto mt-4' : ''}
          `}
        >
          {collapsed ? <FiMenu /> : <FiSidebar />}
        </button>
      </div>

      {/* 导航区域 */}
      <div className="flex-grow overflow-y-auto p-3">
        {isAuthenticated ? (
          <div className="space-y-1">
            {navItems.map((item) => {
              if (item.divider) {
                return (
                  <div
                    key={item.id}
                    className={`my-3 h-px ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'} hover:bg-gray-100`}
                  />
                );
              }
              if (item.children) {
                return (
                  <SubMenu
                    key={item.id}
                    item={item}
                    collapsed={collapsed}
                    activeItemId={activeItemId}
                    theme={theme}
                  />
                );
              }
              return (
                <NavItemLink
                  key={item.id}
                  item={item}
                  collapsed={collapsed}
                  isActive={activeItemId === item.id}
                  theme={theme}
                />
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <p className={`text-gray-500 sidebar-text ${theme === 'dark' ? 'text-white' : ''} mb-4`}>请先登录</p>
            <Link
              to="/auth"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              去登录
            </Link>
          </div>
        )}
      </div>

      {/* 底部区域 - 只在认证状态显示 */}
      {isAuthenticated && (
        <div className={`p-3 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <button
            onClick={handleLogout}
            className={logoutBtnClass}
          >
            <div className="flex items-center">
              <span className={`${collapsed ? 'text-lg' : 'text-base mr-3'}`}>
                <FiLogOut />
              </span>
              {!collapsed && <span>退出登录</span>}
            </div>
          </button>
        </div>
      )}
    </div>
  );
};

export default SidebarNav; 
