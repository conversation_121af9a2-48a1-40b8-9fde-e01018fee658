import React from 'react';
import ReactMarkdown from 'react-markdown';

interface AnalysisReport {
  analysis_text: string;
}

interface AnalysisReportPanelProps {
  report: AnalysisReport | null;
  loading: boolean;
  error: string | null;
}

const AnalysisReportPanel: React.FC<AnalysisReportPanelProps> = ({ report, loading, error }) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <span className="text-gray-500">分析报告加载中...</span>
      </div>
    );
  }
  if (error) {
    return (
      <div className="text-red-500 py-4">分析报告加载失败：{error}</div>
    );
  }
  if (!report) {
    return (
      <div className="text-gray-400 py-4">暂无分析报告数据</div>
    );
  }

  return (
    <div className="space-y-6 p-4 border rounded bg-white shadow">
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown>{report.analysis_text}</ReactMarkdown>
      </div>
    </div>
  );
};

export default AnalysisReportPanel;