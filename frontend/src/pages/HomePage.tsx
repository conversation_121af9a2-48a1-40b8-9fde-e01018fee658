import { GlobeView } from '../components';
import { BuoyProvider, useBuoyContext } from '../contexts/BuoyContext';
import { BuoyInfoSidebar, BuoyListSidebar, SidebarNav, TopNav } from '../components/sidebar';
import { WebSocketConnectionToast } from '../components/websocket';
import { useTheme } from '../contexts/ThemeContext';
import { useLayoutState } from '../hooks';

const HomePageContent = () => {
  const {
    loadingBuoys,
    error,
    buoys
  } = useBuoyContext();
  const { theme } = useTheme();

  // 使用自定义Hook管理布局状态
  const [
    { isMobile, leftSidebarOpen, rightSidebarOpen, navCollapsed, buoyInfoCollapsed },
    { toggleLeftSidebar, toggleRightSidebar, toggleNavCollapsed, toggleBuoyInfoCollapsed }
  ] = useLayoutState();

  // 加载状态渲染
  if (loadingBuoys && buoys.length === 0) {
    return (
      <div className="flex justify-center items-center h-screen bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-900 dark:to-gray-800">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-blue-400 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-lg text-blue-700 dark:text-blue-400 font-medium">正在加载浮标数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen ${theme}`}>
      {/* WebSocket连接状态Toast通知组件 */}
      <WebSocketConnectionToast />

      {/* 顶部导航栏 - 仅在移动端显示 */}
      <TopNav className="md:hidden" />

      {/* 主体区域：左中右三栏布局 */}
      <div className="flex-1 flex flex-col md:flex-row h-0 md:h-full relative">
        {/* 左侧导航栏 */}
        <aside
          className={`
            order-1 md:order-none
            ${isMobile ? (leftSidebarOpen ? 'fixed z-50 left-0 top-0 bottom-0 w-64 bg-white dark:bg-gray-900 shadow-lg' : 'hidden') : (navCollapsed ? 'w-14' : 'w-64')}
            transition-all duration-300 h-full
            border-r ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
          `}
        >
          {!isMobile && (
            <SidebarNav
              collapsed={navCollapsed}
              toggleCollapsed={toggleNavCollapsed}
            />
          )}
        </aside>

        {/* 中间主要内容区域 */}
        <main className="flex-1 flex flex-col relative order-2">
          {/* 桌面端顶部导航栏 */}
          <div className="hidden md:block">
            <TopNav />
          </div>

          {/* 移动端浮动按钮：展开浮标列表 */}
          {isMobile && !rightSidebarOpen && (
            <button
              onClick={toggleRightSidebar}
              className="fixed z-50 bottom-6 right-6 w-14 h-14 rounded-full bg-blue-600 text-white shadow-lg flex items-center justify-center md:hidden hover:bg-blue-700 transition"
              title="展开浮标列表"
              aria-label="展开浮标列表"
              style={{ boxShadow: '0 4px 16px rgba(0,0,0,0.15)' }}
            >
              {/* SplitPanelIcon SVG */}
              <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                <path d="M15 3v18"></path>
              </svg>
            </button>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 bg-red-50 text-red-700 px-4 py-3 rounded-lg shadow-md border border-red-200 flex items-center dark:bg-red-900 dark:text-red-200 dark:border-red-800">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error.message}
            </div>
          )}

          {/* 地球仪视图 */}
          <div className="flex-1 min-h-0">
            <GlobeView />
          </div>
        </main>

        {/* 右侧数据面板 */}
        <aside
          className={`
            order-3 md:order-none
            ${isMobile
              ? (rightSidebarOpen ? 'fixed z-50 right-0 top-0 bottom-0 w-80 bg-white dark:bg-gray-900 shadow-lg' : 'hidden')
              : rightSidebarOpen
                ? (buoyInfoCollapsed ? 'w-16' : 'w-80')
                : 'w-0'
            }
            transition-all duration-300 h-full
            border-l ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
            overflow-hidden
          `}
        >
          {rightSidebarOpen && (
            <div className="flex flex-col h-full">
              <div className="flex-1 overflow-auto">
                <BuoyListSidebar
                  className="w-full"
                  collapsed={buoyInfoCollapsed}
                  toggleCollapsed={toggleBuoyInfoCollapsed}
                />
              </div>
            </div>
          )}
        </aside>
      </div>

    </div>
  );
};

const HomePage = () => {
  return (
    <BuoyProvider>
      <HomePageContent />
    </BuoyProvider>
  );
};

export default HomePage;