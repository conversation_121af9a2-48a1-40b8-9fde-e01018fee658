import { createContext, useReducer, useEffect, ReactNode, useCallback, useRef, useContext } from 'react';
import webSocketClient from '../utils/websocket';
import { WebSocketConnectionState, Subscription, MessageCallback } from '../types';
import { useAuth } from './AuthContext';
import { useToast } from '../components/ui';

// 定义WebSocket事件类型
export type WebSocketEvent = 'connected' | 'disconnected';
export type WebSocketEventCallback = () => void;

// 定义WebSocketContext类型
interface WebSocketContextType {
  // 连接状态
  connectionState: WebSocketConnectionState;
  // 连接方法
  connect: () => Promise<void>;
  // 断开连接方法
  disconnect: () => Promise<void>;
  // 检查是否已连接
  isConnected: () => boolean;
  // 设置认证令牌
  setAuthToken: (token: string | null) => void;
  // 订阅主题
  subscribe: (topic: string, callback: MessageCallback) => Subscription;
  // 取消订阅
  unsubscribe: (subscription: Subscription | string) => void;
  // 获取活跃订阅
  getActiveSubscriptions: () => Map<string, Subscription>;
  // 添加WebSocket事件监听器
  addEventListener: (event: WebSocketEvent, callback: WebSocketEventCallback) => () => void;
  // 移除WebSocket事件监听器
  removeEventListener: (event: WebSocketEvent, callback: WebSocketEventCallback) => void;
}

// 定义WebSocket状态
interface WebSocketState {
  connectionState: WebSocketConnectionState;
  subscriptions: Map<string, Subscription>;
}

// 定义WebSocket动作类型
type WebSocketAction =
  | { type: 'SET_CONNECTION_STATE'; payload: WebSocketConnectionState }
  | { type: 'ADD_SUBSCRIPTION'; payload: Subscription }
  | { type: 'REMOVE_SUBSCRIPTION'; payload: string }
  | { type: 'CLEAR_SUBSCRIPTIONS' };

// WebSocket状态reducer
function webSocketReducer(state: WebSocketState, action: WebSocketAction): WebSocketState {
  switch (action.type) {
    case 'SET_CONNECTION_STATE': {
      return { ...state, connectionState: action.payload };
    }
    case 'ADD_SUBSCRIPTION': {
      const newSubscriptions = new Map(state.subscriptions);
      newSubscriptions.set(action.payload.id, action.payload);
      return { ...state, subscriptions: newSubscriptions };
    }
    case 'REMOVE_SUBSCRIPTION': {
      const updatedSubscriptions = new Map(state.subscriptions);
      updatedSubscriptions.delete(action.payload);
      return { ...state, subscriptions: updatedSubscriptions };
    }
    case 'CLEAR_SUBSCRIPTIONS': {
      return { ...state, subscriptions: new Map() };
    }
    default:
      return state;
  }
}

// 创建Context
const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

// WebSocketProvider属性
interface WebSocketProviderProps {
  children: ReactNode;
  // 是否自动处理认证
  autoAuth?: boolean;
}

/**
 * WebSocket上下文提供者
 * 提供WebSocket连接状态和基本操作
 */
export const WebSocketProvider = ({
  children,
  autoAuth = true
}: WebSocketProviderProps) => {
  // 使用reducer管理状态
  const [state, dispatch] = useReducer(webSocketReducer, {
    connectionState: webSocketClient.getConnectionState(),
    subscriptions: new Map<string, Subscription>()
  });

  // 事件监听器管理
  const eventListeners = useRef<Map<WebSocketEvent, Set<WebSocketEventCallback>>>(new Map());

  // 获取认证上下文
  const { isAuthenticated } = useAuth();

  // 使用Toast通知
  const { showToast } = useToast();

  // 统一的错误处理函数
  const handleError = useCallback((message: string, error?: Error) => {
    console.error(message, error);
    showToast(`${message}: ${error?.message || '未知错误'}`, 'error');
  }, [showToast]);

  // 触发WebSocket事件
  const triggerEvent = useCallback((event: WebSocketEvent) => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback();
        } catch (error) {
          handleError(`执行WebSocket事件监听器时出错: ${event}`, error instanceof Error ? error : new Error('事件监听器错误'));
        }
      });
    }
  }, [handleError]);

  /**
   * 连接WebSocket
   */
  const connect = useCallback(async (): Promise<void> => {
    // 如果组件已卸载，不执行任何操作
    if (isUnmountingRef.current) {
      console.log('组件已卸载，跳过WebSocket连接请求');
      return;
    }

    try {
      // 如果已经连接或正在连接，直接返回
      if (webSocketClient.isConnected() ||
          state.connectionState === WebSocketConnectionState.CONNECTING ||
          state.connectionState === WebSocketConnectionState.RECONNECTING) {
        console.log(`WebSocket已经处于${state.connectionState}状态，跳过连接请求`);
        return;
      }

      // 连接WebSocket
      await webSocketClient.connect();
      console.log('WebSocket连接成功');
    } catch (error) {
      // 如果组件已卸载，不处理错误
      if (!isUnmountingRef.current) {
        handleError('WebSocket连接失败', error instanceof Error ? error : new Error('连接失败'));
      }
      throw error;
    }
  }, [state.connectionState, handleError]);

  /**
   * 断开WebSocket连接
   */
  const disconnect = useCallback(async (): Promise<void> => {
    // 如果组件已卸载，不执行任何操作
    if (isUnmountingRef.current) {
      console.log('组件已卸载，跳过WebSocket断开请求');
      return;
    }

    try {
      // 如果未连接或已经在断开过程中，直接返回
      if (!webSocketClient.isConnected() ||
          state.connectionState === WebSocketConnectionState.CLOSING ||
          state.connectionState === WebSocketConnectionState.CLOSED) {
        console.log(`WebSocket已经处于${state.connectionState}状态，跳过断开请求`);
        return;
      }

      // 断开WebSocket连接
      await webSocketClient.disconnect();
      console.log('WebSocket断开连接');
    } catch (error) {
      // 如果组件已卸载，不处理错误
      if (!isUnmountingRef.current) {
        handleError('WebSocket断开连接失败', error instanceof Error ? error : new Error('断开连接失败'));
      }
      throw error;
    }
  }, [state.connectionState, handleError]);

  /**
   * 检查是否已连接
   */
  const isConnected = useCallback((): boolean => {
    return webSocketClient.isConnected();
  }, []);

  /**
   * 设置认证令牌
   */
  const setAuthToken = useCallback((token: string | null): void => {
    webSocketClient.setAuthToken(token);
    console.log(`WebSocket认证令牌已${token ? '设置' : '清除'}`);
  }, []);

  // 用于标记组件是否已卸载的引用
  const isUnmountingRef = useRef(false);

  // 监听连接状态变化
  useEffect(() => {
    // 重置卸载标志
    isUnmountingRef.current = false;

    // 添加连接状态变化监听器
    const unsubscribe = webSocketClient.onConnectionStateChange((newState) => {
      // 如果组件已卸载，不再更新状态
      if (isUnmountingRef.current) return;

      const prevState = state.connectionState;
      dispatch({ type: 'SET_CONNECTION_STATE', payload: newState });

      // 触发相应的事件
      if (newState === WebSocketConnectionState.OPEN && prevState !== WebSocketConnectionState.OPEN) {
        triggerEvent('connected');
      } else if ((newState === WebSocketConnectionState.CLOSED || newState === WebSocketConnectionState.ERROR) &&
                 prevState === WebSocketConnectionState.OPEN) {
        triggerEvent('disconnected');
      }
    });

    // 组件卸载时清理
    return () => {
      // 标记组件已卸载
      isUnmountingRef.current = true;
      unsubscribe();

      // 当WebSocketProvider组件卸载时，断开WebSocket连接
      // 这确保了只有在HomePage中才会保持WebSocket连接
      if (webSocketClient.isConnected()) {
        console.log('WebSocketProvider组件卸载，断开WebSocket连接');
        webSocketClient.disconnect();
      }
    };
  }, [triggerEvent, state.connectionState, dispatch]);

  // 监听认证状态变化
  useEffect(() => {
    // 如果组件已卸载或不需要自动认证，直接返回
    if (!autoAuth || isUnmountingRef.current) return;

    const token = localStorage.getItem('token');
    const currentConnectionState = state.connectionState;
    const isCurrentlyConnected = webSocketClient.isConnected();

    if (isAuthenticated && token) {
      // 用户已登录，设置认证令牌并连接
      webSocketClient.setAuthToken(token);
      // 只有在未连接状态下才尝试连接
      if (!isCurrentlyConnected && currentConnectionState !== WebSocketConnectionState.CONNECTING) {
        connect();
      }
    } else {
      // 用户已登出，断开连接并清除认证令牌
      if (isCurrentlyConnected || currentConnectionState === WebSocketConnectionState.CONNECTING) {
        disconnect();
      }
      webSocketClient.setAuthToken(null);
    }

    // 清理函数
    return () => {
      // 不在这里执行任何断开连接的操作
      // 所有的断开连接逻辑都由主useEffect的清理函数或显式调用disconnect()处理
    };
  }, [isAuthenticated, autoAuth, state.connectionState, connect, disconnect]);

  /**
   * 订阅主题
   * @param topic 主题
   * @param callback 消息回调函数
   * @returns 订阅对象
   */
  const subscribe = useCallback((topic: string, callback: MessageCallback): Subscription => {
    // 如果组件已卸载，不执行任何操作
    if (isUnmountingRef.current) {
      console.log('组件已卸载，跳过订阅请求');
      // 返回一个空的订阅对象，避免调用方出错
      return {
        id: `dummy-${Date.now()}`,
        topic,
        callback,
        unsubscribe: () => {}
      };
    }

    try {
      // 使用WebSocketClient订阅主题
      const subscription = webSocketClient.subscribe(topic, callback);

      // 更新订阅状态
      dispatch({ type: 'ADD_SUBSCRIPTION', payload: subscription });

      console.log(`已订阅主题: ${topic}, ID: ${subscription.id}`);
      showToast(`已订阅主题: ${topic}`, 'success');

      return subscription;
    } catch (error) {
      // 如果组件已卸载，不处理错误
      if (!isUnmountingRef.current) {
        handleError(`订阅主题 ${topic} 失败`, error instanceof Error ? error : new Error('订阅失败'));
      }

      // 抛出错误，让调用方处理
      throw error;
    }
  }, [showToast, dispatch, handleError]);

  /**
   * 取消订阅
   * @param subscription 订阅对象或订阅ID
   */
  const unsubscribe = useCallback((subscription: Subscription | string): void => {
    // 如果组件已卸载，不执行任何操作
    if (isUnmountingRef.current) {
      console.log('组件已卸载，跳过取消订阅请求');
      return;
    }

    try {
      // 获取订阅ID
      const subscriptionId = typeof subscription === 'string' ? subscription : subscription.id;

      // 检查是否为虚拟订阅（组件卸载时创建的）
      if (subscriptionId.startsWith('dummy-')) {
        console.log(`跳过虚拟订阅的取消: ${subscriptionId}`);
        return;
      }

      // 使用WebSocketClient取消订阅
      webSocketClient.unsubscribe(subscription);

      // 更新订阅状态
      dispatch({ type: 'REMOVE_SUBSCRIPTION', payload: subscriptionId });

      console.log(`已取消订阅: ${subscriptionId}`);
    } catch (error) {
      // 如果组件已卸载，不处理错误
      if (!isUnmountingRef.current) {
        handleError('取消订阅失败', error instanceof Error ? error : new Error('取消订阅失败'));
      }
    }
  }, [dispatch, handleError]);

  /**
   * 获取活跃订阅
   * @returns 活跃订阅Map
   */
  const getActiveSubscriptions = useCallback((): Map<string, Subscription> => {
    return new Map(state.subscriptions);
  }, [state.subscriptions]);

  /**
   * 移除WebSocket事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  const removeEventListener = useCallback((event: WebSocketEvent, callback: WebSocketEventCallback): void => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }, []);

  /**
   * 添加WebSocket事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 移除监听器的函数
   */
  const addEventListener = useCallback((event: WebSocketEvent, callback: WebSocketEventCallback): (() => void) => {
    // 确保事件集合存在
    if (!eventListeners.current.has(event)) {
      eventListeners.current.set(event, new Set());
    }

    // 添加监听器
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.add(callback);
    }

    // 返回移除监听器的函数
    return () => {
      removeEventListener(event, callback);
    };
  }, [removeEventListener]);

  // 在组件卸载时清理所有事件监听器
  useEffect(() => {
    // 保存对eventListeners.current的引用
    const listeners = eventListeners.current;
    return () => {
      listeners.clear();
    };
  }, []);

  // 上下文值
  const value: WebSocketContextType = {
    connectionState: state.connectionState,
    connect,
    disconnect,
    isConnected,
    setAuthToken,
    subscribe,
    unsubscribe,
    getActiveSubscriptions,
    addEventListener,
    removeEventListener
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用WebSocket上下文
export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export default WebSocketContext;
