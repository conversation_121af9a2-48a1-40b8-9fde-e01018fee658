from fastapi import APIRouter

from fastapi_app.api.api_v1.endpoints import auth, users, settings, buoys, data, debug, admin, quiz, analysis

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户"])
api_router.include_router(settings.router, prefix="/settings", tags=["设置"])
api_router.include_router(buoys.router, prefix="/buoys", tags=["浮标"])
api_router.include_router(data.router, prefix="/data", tags=["数据"])
api_router.include_router(debug.router, prefix="/debug", tags=["调试"])
api_router.include_router(admin.router, prefix="/admin", tags=["管理员"])
# api_router.include_router(control.router, prefix="/control", tags=["控制"])
api_router.include_router(quiz.router, prefix="/quiz", tags=["知识问答"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["智能分析"])
# api_router.include_router(notifications.router, prefix="/notifications", tags=["通知"])
# api_router.include_router(feedback.router, prefix="/feedback", tags=["反馈"])