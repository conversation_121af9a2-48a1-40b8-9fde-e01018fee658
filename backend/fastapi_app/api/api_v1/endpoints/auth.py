from datetime import timed<PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from sqlalchemy.ext.asyncio import AsyncSession

from fastapi_app.core.config import settings
from fastapi_app.db.base import get_db
from fastapi_app.models.user_model import User
from fastapi_app.schemas.user import Token, User<PERSON>ogin, UserCreate, User as UserSchema, RefreshToken
from fastapi_app.services.auth_service import (
    create_access_token,
    create_refresh_token,
    authenticate_user,
    validate_token,
    get_user_by_username,
    get_user_by_email,
    update_refresh_token,
    create_user,
    get_user_by_refresh_token
)

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


async def get_current_user(
    db: AsyncSession = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    """从JWT令牌获取当前用户"""
    return await validate_token(db, token)


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """检查用户是否处于活动状态"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已禁用")
    return current_user


@router.post("/login", response_model=Token)
async def login(
    db: AsyncSession = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """登录获取访问令牌"""
    # 验证用户
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 检查用户状态
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户已禁用")

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id), "role": user.role},
        expires_delta=access_token_expires,
    )

    # 创建刷新令牌
    refresh_token = create_refresh_token()

    # 更新数据库中的刷新令牌
    await update_refresh_token(db, user.id, refresh_token)

    return {"access_token": access_token, "token_type": "bearer", "refresh_token": refresh_token}


@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    token_data: RefreshToken, db: AsyncSession = Depends(get_db)
) -> Any:
    """使用刷新令牌获取新的访问令牌"""
    # 查找具有该刷新令牌的用户
    user = await get_user_by_refresh_token(db, token_data.refresh_token)

    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id), "role": user.role},
        expires_delta=access_token_expires,
    )

    # 创建新的刷新令牌
    new_refresh_token = create_refresh_token()

    # 更新数据库中的刷新令牌
    await update_refresh_token(db, user.id, new_refresh_token)

    return {"access_token": access_token, "token_type": "bearer", "refresh_token": new_refresh_token}


@router.post("/logout", response_model=dict)
async def logout(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """用户登出，使刷新令牌失效"""
    # 清除刷新令牌
    await update_refresh_token(db, current_user.id, None)

    return {"message": "登出成功"}


@router.post("/register", response_model=UserSchema)
async def register(
    user_in: UserCreate, db: AsyncSession = Depends(get_db)
) -> Any:
    """注册新用户"""
    # 检查用户名是否已存在
    if await get_user_by_username(db, user_in.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    if await get_user_by_email(db, user_in.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在",
        )

    # 创建新用户
    user = await create_user(db, user_in.username, user_in.email, user_in.password)

    return user