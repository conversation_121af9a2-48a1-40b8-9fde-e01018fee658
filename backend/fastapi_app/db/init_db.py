from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy import text
from datetime import datetime, timedelta, timezone
import random
from sqlalchemy.ext.asyncio import AsyncSession
from geoalchemy2.elements import WKTElement

from fastapi_app.db.base import Base, engine, async_session
from fastapi_app.models.user_model import User
from fastapi_app.models.buoy_model import Buoy
from fastapi_app.models.quiz_model import QuizQuestion, QuizOption, QuizAttempt
from fastapi_app.models.sensor_data_model import SensorData
from fastapi_app.models.additional_model import (
    UserSettings,
    Notification,
    ControlLog,
    Feedback
)
from fastapi_app.services.security import get_password_hash


async def create_tables() -> None:
    """创建所有数据库表"""
    async with engine.begin() as conn:
        # 首先检查并创建PostGIS扩展
        await conn.execute(text("CREATE EXTENSION IF NOT EXISTS postgis;"))
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)

    # 初始化测试数据
    await init_test_data()
    await init_quiz_data()

    print("数据库初始化完成")


async def init_test_data():
    """初始化测试数据"""
    async with async_session() as db:
        # 检查是否已有测试用户
        query = text("SELECT COUNT(*) FROM users WHERE username = 'abc'")
        result = await db.execute(query)
        count = result.scalar()

        # 如果没有测试用户，创建一个
        if count == 0:
            test_user = User(
                username="abc",
                email="<EMAIL>",
                hashed_password=get_password_hash("123456"),
                role="user",
                is_active=True
            )
            db.add(test_user)
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("123456"),
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            await db.commit()
            await db.refresh(test_user)

            # 添加测试浮标数据
            # await add_test_buoys(db, test_user.id)


async def add_test_buoys(db: AsyncSession, user_id: int):
    """添加测试浮标数据"""
    # 检查是否已有测试浮标
    query = text("SELECT COUNT(*) FROM buoys")
    result = await db.execute(query)
    count = result.scalar()

    # 如果没有测试浮标，创建几个
    if count == 0:
        # 创建3个测试浮标
        buoy_data = [
            {
                "name": "天津海域浮标01",
                "description": "位于天津近海的水质监测浮标",
                "location": WKTElement('POINT(117.78 38.98)', srid=4326),  # 天津附近海域
                "status": "active"
            },
            {
                "name": "青岛海域浮标02",
                "description": "位于青岛近海的水质监测浮标",
                "location": WKTElement('POINT(120.38 36.06)', srid=4326),  # 青岛附近海域
                "status": "active"
            },
            {
                "name": "大连海域浮标03",
                "description": "位于大连近海的水质监测浮标",
                "location": WKTElement('POINT(121.62 38.92)', srid=4326),  # 大连附近海域
                "status": "inactive"
            }
        ]

        for data in buoy_data:
            buoy = Buoy(
                name=data["name"],
                description=data["description"],
                owner_id=user_id,
                latest_location=data["location"],
                status=data["status"],
                last_heartbeat=datetime.now(timezone.utc) - timedelta(minutes=random.randint(5, 60))
            )
            db.add(buoy)

        await db.commit()

        # 为每个浮标添加一些传感器数据
        buoy_query = text("SELECT id FROM buoys")
        result = await db.execute(buoy_query)
        buoy_ids = [row[0] for row in result.fetchall()]

        # 添加不同类型的传感器数据
        for buoy_id in buoy_ids:
            # 添加水质传感器数据（pH值、溶解氧、水温）
            now = datetime.now(timezone.utc)
            for i in range(24):  # 添加过去24小时的数据，每小时一条
                timestamp = now - timedelta(hours=i)

                # pH值数据（范围：6.5-8.5）
                ph_data = SensorData(
                    buoy_id=buoy_id,
                    timestamp=timestamp,
                    data_type="ph",
                    value=round(7.0 + random.uniform(-0.5, 1.5), 2),
                    unit="pH"
                )
                db.add(ph_data)

                # 溶解氧数据（范围：4-10 mg/L）
                do_data = SensorData(
                    buoy_id=buoy_id,
                    timestamp=timestamp,
                    data_type="dissolved_oxygen",
                    value=round(7.0 + random.uniform(-3.0, 3.0), 2),
                    unit="mg/L"
                )
                db.add(do_data)

                # 水温数据（范围：15-25℃）
                temp_data = SensorData(
                    buoy_id=buoy_id,
                    timestamp=timestamp,
                    data_type="temperature",
                    value=round(20.0 + random.uniform(-5.0, 5.0), 1),
                    unit="°C"
                )
                db.add(temp_data)

                # 位置数据（微小变动）
                # 获取浮标初始位置
                location_query = text("SELECT ST_AsText(latest_location) FROM buoys WHERE id = :id")
                location_result = await db.execute(location_query, {"id": buoy_id})
                location_wkt = location_result.scalar()

                # 解析WKT字符串获取经纬度 (例如："POINT(117.78 38.98)")
                if location_wkt:
                    coords = location_wkt.replace("POINT(", "").replace(")", "").split()
                    lon = float(coords[0])
                    lat = float(coords[1])

                    # 添加随机微小变动（±0.01度）
                    new_lon = lon + random.uniform(-0.01, 0.01)
                    new_lat = lat + random.uniform(-0.01, 0.01)

                    location_data = SensorData(
                        buoy_id=buoy_id,
                        timestamp=timestamp,
                        data_type="location",
                        location_value=WKTElement(f'POINT({new_lon} {new_lat})', srid=4326)
                    )
                    db.add(location_data)

        await db.commit()


async def drop_tables() -> None:
    """删除所有数据库表（谨慎使用）"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    print("数据库表已删除")

async def init_quiz_data():
    """初始化知识问答测试数据"""
    async with async_session() as db:
        # 检查是否已有测试题目
        query = text("SELECT COUNT(*) FROM quiz_questions")
        result = await db.execute(query)
        count = result.scalar()

        # 如果没有测试题目，创建一些
        if count == 0:
            quiz_data = [
                {
                    "content": "浮标的主要功能是什么？",
                    "category": "浮标知识",
                    "difficulty": "简单",
                    "options": [
                        {"text": "装饰水面", "correct": False},
                        {"text": "监测环境数据", "correct": True},
                        {"text": "提供照明", "correct": False},
                        {"text": "作为导航标志", "correct": False}
                    ],
                    "correct_answer_id": 2
                },
                {
                    "content": "浮标通常监测哪些环境参数？",
                    "category": "浮标知识",
                    "difficulty": "中等",
                    "options": [
                        {"text": "水质和气象", "correct": True},
                        {"text": "空气质量", "correct": False},
                        {"text": "土壤湿度", "correct": False},
                        {"text": "噪音水平", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标如何传输数据？",
                    "category": "浮标知识",
                    "difficulty": "中等",
                    "options": [
                        {"text": "通过电缆", "correct": False},
                        {"text": "通过无线网络", "correct": True},
                        {"text": "通过邮寄", "correct": False},
                        {"text": "通过人工读取", "correct": False}
                    ],
                    "correct_answer_id": 2
                },
                {
                    "content": "浮标的位置如何确定？",
                    "category": "浮标知识",
                    "difficulty": "简单",
                    "options": [
                        {"text": "通过GPS", "correct": True},
                        {"text": "通过人工测量", "correct": False},
                        {"text": "通过星座导航", "correct": False},
                        {"text": "通过声呐", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标在海洋研究中的作用是什么？",
                    "category": "浮标知识",
                    "difficulty": "困难",
                    "options": [
                        {"text": "收集海洋数据", "correct": True},
                        {"text": "作为潜水员的休息点", "correct": False},
                        {"text": "作为渔船的停靠点", "correct": False},
                        {"text": "作为海洋生物的栖息地", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标如何应对恶劣天气？",
                    "category": "浮标知识",
                    "difficulty": "困难",
                    "options": [
                        {"text": "通过加固结构", "correct": True},
                        {"text": "通过潜入水下", "correct": False},
                        {"text": "通过自动移动到安全区域", "correct": False},
                        {"text": "通过关闭所有传感器", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标的能源来源通常是什么？",
                    "category": "浮标知识",
                    "difficulty": "中等",
                    "options": [
                        {"text": "太阳能和电池", "correct": True},
                        {"text": "风能", "correct": False},
                        {"text": "核能", "correct": False},
                        {"text": "化石燃料", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标如何进行维护？",
                    "category": "浮标知识",
                    "difficulty": "困难",
                    "options": [
                        {"text": "定期检查和维修", "correct": True},
                        {"text": "完全自动化，无需维护", "correct": False},
                        {"text": "通过远程软件更新", "correct": False},
                        {"text": "通过更换整个浮标", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标在环境保护中的作用是什么？",
                    "category": "浮标知识",
                    "difficulty": "简单",
                    "options": [
                        {"text": "监测污染水平", "correct": True},
                        {"text": "清理海洋垃圾", "correct": False},
                        {"text": "防止非法捕鱼", "correct": False},
                        {"text": "保护海洋生物", "correct": False}
                    ],
                    "correct_answer_id": 1
                },
                {
                    "content": "浮标如何实现实时数据传输？",
                    "category": "浮标知识",
                    "difficulty": "中等",
                    "options": [
                        {"text": "通过卫星通信", "correct": True},
                        {"text": "通过海底电缆", "correct": False},
                        {"text": "通过短波无线电", "correct": False},
                        {"text": "通过信鸽", "correct": False}
                    ],
                    "correct_answer_id": 1
                }
            ]

            for data in quiz_data:
                question = QuizQuestion(
                    content=data["content"],
                    category=data["category"],
                    difficulty=data["difficulty"],
                    correct_answer_id=data["correct_answer_id"]
                )
                db.add(question)
                await db.flush()  # 确保question有id
                print(f"添加问题: {data['content']}, 初始 correct_answer_id: {question.correct_answer_id}")

                for i, option_data in enumerate(data["options"], 1):
                    option = QuizOption(
                        question_id=question.id,
                        text=option_data["text"]
                    )
                    db.add(option)
                    await db.flush()  # 确保option有id
                    print(f"添加选项: {option_data['text']}, 选项ID: {option.id}, 是否正确: {option_data.get('correct', False)}")
                    if option_data.get("correct", False):
                        question.correct_answer_id = option.id
                        print(f"更新 correct_answer_id 为 {option.id}")

        await db.commit()
        print("知识问答测试数据初始化完成")