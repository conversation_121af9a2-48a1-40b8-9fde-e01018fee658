import json
import re
import httpx
from typing import Any, Dict, List
from fastapi_app.core.config import Settings
from fastapi_app.schemas.analysis import AnalysisReportRequest, AnalysisReportResponse, AnalysisRequestItem
import logging

settings = Settings()

logger = logging.getLogger("llm_service")

class LLMServiceException(Exception):
    pass

class LLMService:
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.api_endpoint = settings.DEEPSEEK_API_ENDPOINT.rstrip("/")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    async def analyze_report(self, req: AnalysisReportRequest) -> AnalysisReportResponse:
        # 将多个分析项合并为一个分析请求
        prompt = self._build_prompt(req.items)
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": """你是一个智能浮标数据分析助手。请根据传感器历史数据，生成一份完整的分析报告。

请按照以下结构组织你的分析报告：

1. 数据概览
   - 简要描述数据的时间范围和基本特征
   - 关键统计指标（如平均值、最大值、最小值等）

2. 趋势分析
   - 详细分析数据的变化趋势
   - 识别任何周期性模式或异常波动
   - 解释可能的原因

3. 异常检测
   - 指出并分析任何异常值或异常模式
   - 解释这些异常的可能原因
   - 评估这些异常的影响

4. 建议措施
   - 基于分析结果提供具体的改进建议
   - 提出预防措施或优化方案

请用清晰、专业的中文撰写报告，确保内容准确、完整且易于理解。"""
                },
                {"role": "user", "content": prompt}
            ],
            "stream": True,
            "timeout": 60
        }
        logger.info(f"准备调用 LLM API，请求数据: {json.dumps(payload, ensure_ascii=False)}")
        
        for attempt in range(2):
            try:
                async with httpx.AsyncClient(
                    timeout=httpx.Timeout(
                        connect=10.0,
                        read=60.0,
                        write=10.0,
                        pool=10.0
                    )
                ) as client:
                    logger.info(f"开始第 {attempt + 1} 次 LLM API 调用")
                    try:
                        async with client.stream(
                            "POST",
                            f"{self.api_endpoint}/chat/completions",
                            headers=self.headers,
                            json=payload
                        ) as response:
                            logger.info(f"LLM API 响应状态码: {response.status_code}")
                            
                            response.raise_for_status()
                            
                            # 收集流式响应
                            full_content = ""
                            buffer = ""
                            async for chunk in response.aiter_text():
                                try:
                                    buffer += chunk
                                    while "\n\n" in buffer:
                                        line, buffer = buffer.split("\n\n", 1)
                                        if line.startswith("data: "):
                                            data = line[6:]
                                            if data.strip() == "[DONE]":
                                                break
                                            try:
                                                chunk_data = json.loads(data)
                                                if "choices" in chunk_data and chunk_data["choices"]:
                                                    delta = chunk_data["choices"][0].get("delta", {})
                                                    if "content" in delta:
                                                        full_content += delta["content"]
                                            except json.JSONDecodeError as e:
                                                logger.warning(f"解析 SSE 数据失败: {str(e)}, data: {data}")
                                                continue
                                except Exception as e:
                                    logger.warning(f"处理流式响应时出错: {str(e)}, chunk: {chunk}")
                                    continue
                            
                            if not full_content:
                                raise LLMServiceException("LLM 返回内容为空")
                                
                            logger.info(f"收集到的完整响应内容: {full_content}")
                            
                            # 直接返回分析文本
                            return AnalysisReportResponse(analysis_text=full_content.strip())
                            
                    except httpx.HTTPStatusError as e:
                        if e.response.status_code == 429:
                            raise LLMServiceException("分析服务当前负载较高，请稍后重试")
                        elif e.response.status_code >= 500:
                            raise LLMServiceException("分析服务暂时不可用，请稍后重试")
                        else:
                            raise LLMServiceException(f"分析请求失败: {e.response.status_code}")
                    except httpx.TimeoutException as e:
                        raise LLMServiceException("分析服务响应超时，请稍后重试")
                    except httpx.RequestError as e:
                        raise LLMServiceException("无法连接到分析服务，请检查网络连接")
                    except Exception as e:
                        logger.error(f"LLM API 调用未知错误: {str(e)}", exc_info=True)
                        raise LLMServiceException("分析服务发生未知错误，请稍后重试")
                
            except LLMServiceException as e:
                logger.error(f"第 {attempt + 1} 次 LLM 调用失败: {str(e)}", exc_info=True)
                if attempt == 1:
                    raise
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次 LLM 调用发生未知错误: {str(e)}", exc_info=True)
                if attempt == 1:
                    raise LLMServiceException("分析服务发生未知错误，请稍后重试")
                    
        raise LLMServiceException("分析服务暂时不可用，请稍后重试")

    def _build_prompt(self, items: List[AnalysisRequestItem]) -> str:
        # 构建更结构化的提示，包含多个传感器数据
        prompt_data = {
            "task_description": "请分析以下浮标传感器数据，生成完整的分析报告。",
            "sensors": [
                {
                    "buoy_id": item.buoy_id,
                    "sensor_type": item.sensor_type,
                    "data": item.data.dict()
                }
                for item in items
            ]
        }
        return json.dumps(prompt_data, ensure_ascii=False, indent=2)

    def _extract_content(self, result: Dict[str, Any]) -> str:
        """此方法已不再使用，保留是为了向后兼容"""
        logger.warning("_extract_content 方法已弃用，直接使用分析报告内容")
        if isinstance(result, dict) and "analysis_report" in result:
            return json.dumps(result["analysis_report"])
        raise LLMServiceException(f"无效的响应格式: {json.dumps(result, ensure_ascii=False)}")

    def _parse_llm_output(self, content: str) -> AnalysisReportResponse:
        """此方法已不再使用，保留是为了向后兼容"""
        logger.warning("_parse_llm_output 方法已弃用，直接使用分析报告内容")
        try:
            data = json.loads(content)
            if isinstance(data, dict) and "analysis_report" in data:
                analysis_report = data["analysis_report"]
                
                # 获取趋势描述（支持多种可能的字段名）
                trend_description = None
                for trend_field in ["trend_analysis", "temperature_trend"]:
                    if trend_field in analysis_report:
                        trend_data = analysis_report[trend_field]
                        if isinstance(trend_data, dict):
                            trend_description = trend_data.get("description")
                            if trend_description:
                                break
                
                if not trend_description:
                    # 如果没有找到趋势描述，使用统计信息生成一个
                    stats = analysis_report.get("statistics", {})
                    trend_description = (
                        f"温度范围: {stats.get('minimum', 'N/A')}°C 至 {stats.get('maximum', 'N/A')}°C, "
                        f"平均值: {stats.get('average', 'N/A')}°C, "
                        f"标准差: {stats.get('standard_deviation', 'N/A')}°C"
                    )
                
                # 获取异常点（支持不同的字段格式）
                anomalies = []
                if "anomalies" in analysis_report:
                    for anomaly in analysis_report["anomalies"]:
                        anomaly_data = {
                            "timestamp": anomaly["timestamp"],
                            "value": anomaly["value"],
                            "desc": anomaly.get("description") or anomaly.get("reason", "未知异常")
                        }
                        if "deviation" in anomaly:
                            anomaly_data["desc"] += f" (偏差: {anomaly['deviation']})"
                        anomalies.append(anomaly_data)
                
                # 获取建议（支持不同的字段名）
                recommendations = analysis_report.get("recommendations", [])
                if not recommendations and "environmental_observations" in analysis_report:
                    env_obs = analysis_report["environmental_observations"]
                    if "potential_causes" in env_obs:
                        recommendations.extend(env_obs["potential_causes"])
                
                return AnalysisReportResponse(
                    trend=trend_description,
                    anomalies=anomalies,
                    forecast=None,
                    suggestion="\n".join(recommendations) if recommendations else None
                )
            raise LLMServiceException("无效的分析报告格式")
        except json.JSONDecodeError as e:
            raise LLMServiceException(f"无法解析分析报告: {str(e)}")