import json
import time
import random
import logging
import signal
import sys
from datetime import datetime, timezone
import paho.mqtt.client as mqtt
import threading
from config import *

# 配置日志
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'INFO'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('buoy_simulator')

# 全局变量，用于控制程序退出
shutdown_event = threading.Event()

class BuoySimulator:
    def __init__(self, buoy_id):
        self.buoy_id = buoy_id
        self.client_id = f"mock-buoy-device-{buoy_id}"
        self.client = mqtt.Client(client_id=self.client_id, callback_api_version=mqtt.CallbackAPIVersion.VERSION1)

        # 设置认证信息
        if MQTT_USERNAME:
            self.client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)

        self.connected = False
        self.running = False

        # 设置LWT（遗嘱消息）
        self.status_topic = f"buoy/{self.buoy_id}/status"
        self.client.will_set(self.status_topic, payload="offline", qos=1, retain=False)

        # 设置回调
        self.client.on_connect = self.on_connect
        self.client.on_disconnect = self.on_disconnect
        self.client.on_message = self.on_message

        # 订阅控制主题
        self.control_topic = f"buoy/command/{self.buoy_id}"

        # 浮标状态
        self.status = "active"
        self.light_brightness = 0
        self.light_color = "#FFFFFF"

        # 打印环境变量信息
        logger.info(f"DATA_INTERVAL_MIN: {DATA_INTERVAL_MIN}")
        logger.info(f"DATA_INTERVAL_MAX: {DATA_INTERVAL_MAX}")


    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            logger.info(f"浮标 {self.buoy_id} 已连接到MQTT服务器")
            self.connected = True
            # 订阅控制主题
            self.client.subscribe(self.control_topic)
            logger.info(f"已订阅控制主题: {self.control_topic}")
            # 上线时主动发布online
            self.client.publish(self.status_topic, "online", qos=1, retain=False)
        else:
            logger.error(f"浮标 {self.buoy_id} 连接失败，返回码: {rc}")

    def on_disconnect(self, client, userdata, rc):
        logger.info(f"浮标 {self.buoy_id} 已断开MQTT连接")
        self.connected = False

    def on_message(self, client, userdata, msg):
        try:
            payload = json.loads(msg.payload.decode())
            logger.info(f"浮标 {self.buoy_id} 收到控制命令: {payload}")

            if msg.topic == self.control_topic:
                self.handle_control_command(payload)
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")

    def handle_control_command(self, command):
        """处理接收到的控制命令"""
        if "command" not in command:
            return

        if command["command"] == "set_light":
            if "brightness" in command:
                self.light_brightness = command["brightness"]
            if "color" in command:
                self.light_color = command["color"]

            # 发送确认响应
            response_topic = f"buoy/command/response/{self.buoy_id}"
            response = {
                "buoy_id": self.buoy_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "success",
                "command": command["command"],
                "current_settings": {
                    "brightness": self.light_brightness,
                    "color": self.light_color
                }
            }
            self.client.publish(response_topic, json.dumps(response))
            logger.info(f"已发送控制响应: {response}")

    def generate_sensor_data(self):
        """生成模拟的传感器数据"""
        data_types = {
            "temperature": {"value": round(random.uniform(15, 30), 2), "unit": "°C"},
            "ph": {"value": round(random.uniform(6.5, 8.5), 2), "unit": "pH"},
            "dissolved_oxygen": {"value": round(random.uniform(5, 12), 2), "unit": "mg/L"},
            "water_level": {"value": round(random.uniform(0.5, 2.5), 2), "unit": "m"}
        }

        # 随机选择一个或多个数据类型
        return {k: v for k, v in data_types.items() if random.random() > 0.3}

    def generate_location_data(self):
        """生成模拟的位置数据"""
        # 固定一个基准位置，然后随机偏移
        return {
            "longitude": BASE_LONGITUDE + random.uniform(-0.01, 0.01),
            "latitude": BASE_LATITUDE + random.uniform(-0.01, 0.01)
        }

    def connect(self):
        """连接到MQTT服务器"""
        try:
            # 如果启用了TLS，配置TLS连接
            if MQTT_USE_TLS:
                import ssl
                # 创建SSL上下文
                ssl_context = ssl.create_default_context()

                # 如果配置了CA证书路径，则加载证书
                if MQTT_CA_CERT_PATH:
                    try:
                        ssl_context.load_verify_locations(cafile=MQTT_CA_CERT_PATH)
                        logger.info(f"已加载MQTT CA证书: {MQTT_CA_CERT_PATH}")
                    except Exception as cert_err:
                        logger.error(f"加载MQTT CA证书失败: {str(cert_err)}")
                        # 如果证书加载失败，可以选择继续（不验证服务器证书）或抛出异常
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE
                        logger.warning("已禁用MQTT服务器证书验证")

                # 设置TLS
                self.client.tls_set_context(ssl_context)
                logger.info(f"已启用MQTT TLS连接")

            # 连接到MQTT服务器
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            logger.info(f"已连接到MQTT服务器: {MQTT_BROKER}:{MQTT_PORT} (TLS: {MQTT_USE_TLS})")
            return True
        except Exception as e:
            logger.error(f"连接MQTT服务器时出错: {e}")
            return False

    def disconnect(self):
        """断开MQTT连接"""
        try:
            # 先停止循环，再断开连接
            self.client.loop_stop()
            # 只有在已连接状态下才尝试断开连接
            if self.connected:
                # 主动发布offline
                self.client.publish(self.status_topic, "offline", qos=1, retain=False)
                time.sleep(0.2)  # 确保offline消息发出
                self.client.disconnect()
                logger.debug(f"浮标 {self.buoy_id} MQTT客户端已断开连接")
        except Exception as e:
            logger.error(f"浮标 {self.buoy_id} 断开MQTT连接时出错: {e}")
        finally:
            self.connected = False

    def start(self):
        """开始模拟数据发送"""
        if not self.connect():
            return False

        self.running = True
        # 创建线程并命名，便于调试
        thread = threading.Thread(
            target=self._run_simulation,
            name=f"buoy-{self.buoy_id}-thread"
        )
        thread.start()
        return True

    def stop(self):
        """停止模拟"""
        logger.info(f"正在停止浮标 {self.buoy_id} 模拟器...")
        self.running = False
        # 发送最后一条状态消息，表明浮标已停止
        try:
            if self.connected:
                self.status = "inactive"
                heartbeat_topic = f"buoy/heartbeat/{self.buoy_id}"
                heartbeat_payload = {
                    "buoy_id": self.buoy_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "status": self.status
                }
                self.client.publish(heartbeat_topic, json.dumps(heartbeat_payload))
                logger.debug(f"浮标 {self.buoy_id} 已发送停止状态")
        except Exception as e:
            logger.error(f"浮标 {self.buoy_id} 发送停止状态时出错: {e}")

    def _run_simulation(self):
        """运行模拟循环"""
        while self.running and not shutdown_event.is_set():
            try:
                if not self.connected:
                    # 使用较短的睡眠时间，以便更快地响应关闭信号
                    for _ in range(5):
                        if shutdown_event.is_set():
                            return
                        time.sleep(1)
                    continue

                # 发送传感器数据
                sensor_data = self.generate_sensor_data()
                for data_type, data in sensor_data.items():
                    if shutdown_event.is_set():
                        return
                    topic = f"{TOPIC_PREFIX}{self.buoy_id}/{data_type}"
                    payload = {
                        "buoy_id": self.buoy_id,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "data_type": data_type,
                        "value": data["value"],
                        "unit": data["unit"]
                    }
                    self.client.publish(topic, json.dumps(payload))
                    logger.debug(f"已发布到 {topic}: {payload}")

                # 发送位置数据
                if shutdown_event.is_set():
                    return
                location = self.generate_location_data()
                location_topic = f"{TOPIC_PREFIX}{self.buoy_id}/location"
                location_payload = {
                    "buoy_id": self.buoy_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "data_type": "location",
                    "value": location,
                    "unit": "WGS84"
                }
                self.client.publish(location_topic, json.dumps(location_payload))
                logger.debug(f"已发布到 {location_topic}: {location_payload}")

                # 发送心跳
                if shutdown_event.is_set():
                    return
                heartbeat_topic = f"buoy/heartbeat/{self.buoy_id}"
                heartbeat_payload = {
                    "buoy_id": self.buoy_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "status": self.status
                }
                self.client.publish(heartbeat_topic, json.dumps(heartbeat_payload))

                # 等待一段时间，但分成小段，以便能够更快地响应关闭信号
                wait_time = random.uniform(DATA_INTERVAL_MIN, DATA_INTERVAL_MAX)
                logger.debug(f"浮标 {self.buoy_id} 等待 {wait_time:.2f} 秒")

                # 将等待时间分成多个小段，每段最多1秒
                wait_end_time = time.time() + wait_time
                while time.time() < wait_end_time and not shutdown_event.is_set():
                    time.sleep(min(1.0, wait_end_time - time.time()))
            except Exception as e:
                logger.error(f"模拟过程中出错: {e}")
                # 使用较短的睡眠时间，以便更快地响应关闭信号
                for _ in range(5):
                    if shutdown_event.is_set():
                        return
                    time.sleep(1)


def signal_handler(sig, frame):
    """处理信号"""
    logger.info(f"接收到信号 {sig}，准备关闭...")
    shutdown_event.set()


def shutdown_buoys(buoys, timeout=10):
    """优雅地关闭所有浮标模拟器"""
    logger.info(f"正在关闭 {len(buoys)} 个浮标模拟器，超时时间 {timeout} 秒...")

    # 首先停止所有模拟器
    for buoy in buoys:
        buoy.stop()

    # 设置关闭事件，通知所有线程退出
    shutdown_event.set()

    # 等待所有线程完成，但最多等待timeout秒
    shutdown_start = time.time()
    active_threads = True

    while active_threads and (time.time() - shutdown_start < timeout):
        active_threads = False
        for thread in threading.enumerate():
            if thread != threading.current_thread() and not thread.daemon:
                active_threads = True
                logger.debug(f"等待线程完成: {thread.name}")
                thread.join(0.5)  # 短暂等待，然后检查超时
                break

    # 断开所有MQTT连接
    for buoy in buoys:
        try:
            buoy.disconnect()
            logger.info(f"浮标 {buoy.buoy_id} 已断开连接")
        except Exception as e:
            logger.error(f"断开浮标 {buoy.buoy_id} 连接时出错: {e}")

    # 检查是否超时
    if active_threads:
        logger.warning(f"关闭超时 ({timeout}秒)，仍有活动线程")
    else:
        logger.info("所有线程已正常退出")


def main():
    """主函数"""
    logger.info("启动浮标模拟器")

    # 注册信号处理程序
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    buoys = []
    try:
        # 创建并启动多个浮标模拟器
        for i in range(1, BUOY_COUNT + 1):
            buoy = BuoySimulator(i)
            if buoy.start():
                buoys.append(buoy)
                logger.info(f"浮标 {i} 模拟器已启动")
            else:
                logger.error(f"浮标 {i} 模拟器启动失败")

        # 保持程序运行，直到收到关闭信号
        while not shutdown_event.is_set():
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到键盘中断，准备关闭...")
        shutdown_event.set()
    finally:
        # 优雅地关闭所有浮标模拟器
        shutdown_buoys(buoys)
        logger.info("浮标模拟器已关闭")


if __name__ == "__main__":
    main()